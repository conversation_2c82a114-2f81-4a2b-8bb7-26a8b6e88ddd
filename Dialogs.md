# GameDialog System Documentation

A dialog system for DrawnUI game with navigation stack support, customizable animations, and fluent API.

## Table of Contents

1. [Basic Usage](#basic-usage)
2. [Navigation Stack](#navigation-stack)
3. [Animations](#animations)
4. [API Reference](#api-reference)
5. [Examples](#examples)

## Basic Usage

### Simple Dialog (Fire-and-forget)

```csharp
var content = new SkiaLabel()
{
    Text = "Welcome to the game!",
    TextColor = Colors.White,
    FontSize = 16,
    HorizontalTextAlignment = DrawTextAlignment.Center,
    HorizontalOptions = LayoutOptions.Fill,
};

GameDialog.Show(this, content, "START", () =>
{
    // Handle OK button click
    StartGame();
});
```

Just an info dialof with no result:

```csharp
var content = new SkiaLabel()
{
    Text = "Welcome to the game!",
    TextColor = Colors.White,
    FontSize = 16,
    HorizontalTextAlignment = DrawTextAlignment.Center,
    HorizontalOptions = LayoutOptions.Fill,
};

GameDialog.Show(this, content, "START", () =>
{
    // Handle OK button click
    StartGame();
});
```

### Dialog with OK and Cancel

```csharp
GameDialog.Show(this, content, "SAVE", "CANCEL", 
    onOk: () => SaveGame(),
    onCancel: () => CancelSave());
```

### Async Dialog (Wait for Result)

```csharp
bool result = await GameDialog.ShowAsync(this, content, "YES", "NO");

if (result)
{
    // User clicked YES
    DoSomething();
}
else
{
    // User clicked NO
    DoSomethingElse();
}
```

## Navigation Stack

The dialog system includes a powerful navigation stack that tracks dialogs per container.

### Key Concepts

- **Both `Show` and `Push` add to the stack**
- **Stack is per-container**: Each `SkiaLayout` has its own dialog stack
- **Automatic cleanup**: Empty stacks are removed automatically
- **LIFO behavior**: Last dialog shown is first to be popped

### Push Dialogs

```csharp
// Push first dialog
GameDialog.Push(this, content1, "NEXT", "CANCEL", 
    onOk: () => {
        // Push second dialog on top
        GameDialog.Push(this, content2, "FINISH", "BACK",
            onOk: () => GameDialog.PopAll(this),      // Close all
            onCancel: () => GameDialog.Pop(this));    // Go back one
    },
    onCancel: () => GameDialog.PopAll(this));
```

### Pop Operations

```csharp
// Pop topmost dialog (no result)
await GameDialog.Pop(container);

// Pop with animation control
await GameDialog.Pop(container, animate: false);

// Pop and wait for animation to finish
await GameDialog.PopAsync(container);

// Pop all dialogs (like "pop to root")
await GameDialog.PopAll(container);
await GameDialog.PopAllAsync(container);

// Check stack depth
int count = GameDialog.GetStackCount(container);
```

## Animations

### Default Animations

By default, dialogs use placeholder animations (`Task.Delay(1)`). You can customize them globally or per-dialog.

### Global Animation Customization

```csharp
// Set global appearing animation
GameDialog.DefaultAppearingAnimation = async (dialog) =>
{
    dialog.Scale = 0.5f;
    dialog.Opacity = 0.0;
    
    var scaleTask = dialog.ScaleTo(1.0, 250, Easing.CubicOut);
    var fadeTask = dialog.FadeTo(1.0, 200, Easing.Linear);
    
    await Task.WhenAll(scaleTask, fadeTask);
};

// Set global disappearing animation
GameDialog.DefaultDisappearingAnimation = async (dialog) =>
{
    var scaleTask = dialog.ScaleTo(0.8, 150, Easing.CubicIn);
    var fadeTask = dialog.FadeTo(0.0, 150, Easing.Linear);
    
    await Task.WhenAll(scaleTask, fadeTask);
};
```

### Custom Dialog with Override

```csharp
public class CustomDialog : GameDialog
{
    protected override async Task PlayAppearingAnimation()
    {
        // Custom slide-in from top
        TranslationY = -200;
        Opacity = 0;
        
        var slideTask = this.TranslateTo(0, 0, 300, Easing.BounceOut);
        var fadeTask = this.FadeTo(1.0, 250, Easing.Linear);
        
        await Task.WhenAll(slideTask, fadeTask);
    }
    
    protected override async Task PlayDisappearingAnimation()
    {
        // Custom slide-out to bottom
        var slideTask = this.TranslateTo(0, 200, 200, Easing.CubicIn);
        var fadeTask = this.FadeTo(0.0, 200, Easing.Linear);
        
        await Task.WhenAll(slideTask, fadeTask);
    }
}
```

## API Reference

### Static Methods

#### Show Methods
```csharp
// Basic show (adds to stack)
static void Show(SkiaLayout container, SkiaControl content, 
                string ok = null, string cancel = null, 
                Action onOk = null, Action onCancel = null)

// Async show (adds to stack, returns result)
static Task<bool> ShowAsync(SkiaLayout container, SkiaControl content, 
                           string ok = null, string cancel = null)
```

#### Navigation Stack Methods
```csharp
// Push dialog (same as Show, but explicit about stack behavior)
static void Push(SkiaLayout container, SkiaControl content, 
                string ok = null, string cancel = null, 
                Action onOk = null, Action onCancel = null)

// Pop operations
static Task Pop(SkiaLayout container, bool animate = true)
static Task PopAsync(SkiaLayout container, bool animate = true)
static Task PopAll(SkiaLayout container, bool animate = true)
static Task PopAllAsync(SkiaLayout container, bool animate = true)

// Stack info
static int GetStackCount(SkiaLayout container)
```

### Instance Methods

```csharp
// Close with specific result
Task CloseAsync(bool result, bool animate = true)

// Convenience close methods
Task CloseWithOkAsync(bool animate = true)
Task CloseWithCancelAsync(bool animate = true)
```

### Animation Properties

```csharp
// Global animation delegates
static Func<GameDialog, Task> DefaultAppearingAnimation { get; set; }
static Func<GameDialog, Task> DefaultDisappearingAnimation { get; set; }
```

### Virtual Methods (Override in Subclasses)

```csharp
protected virtual Task PlayAppearingAnimation()
protected virtual Task PlayDisappearingAnimation()
```

## Examples

### Game Menu System

```csharp
void ShowMainMenu()
{
    var menuContent = new SkiaLabel()
    {
        Text = "Main Menu",
        TextColor = Colors.White,
        FontSize = 20,
        HorizontalTextAlignment = DrawTextAlignment.Center,
        HorizontalOptions = LayoutOptions.Fill,
    };

    GameDialog.Show(this, menuContent, "PLAY", "SETTINGS",
        onOk: () => StartGame(),
        onCancel: () => ShowSettings());
}

void ShowSettings()
{
    var settingsContent = new SkiaLabel()
    {
        Text = "Settings Menu",
        TextColor = Colors.White,
        FontSize = 16,
        HorizontalTextAlignment = DrawTextAlignment.Center,
        HorizontalOptions = LayoutOptions.Fill,
    };

    GameDialog.Push(this, settingsContent, "BACK", onOk: () =>
    {
        GameDialog.Pop(this); // Go back to main menu
    });
}
```

### Confirmation Dialog

```csharp
async Task<bool> ConfirmQuit()
{
    var content = new SkiaLabel()
    {
        Text = "Are you sure you want to quit?",
        TextColor = Colors.White,
        FontSize = 16,
        HorizontalTextAlignment = DrawTextAlignment.Center,
        HorizontalOptions = LayoutOptions.Fill,
    };

    return await GameDialog.ShowAsync(this, content, "YES", "NO");
}

// Usage
if (await ConfirmQuit())
{
    Application.Current.Quit();
}
```

### Multi-Step Wizard

```csharp
void ShowWizard()
{
    ShowStep1();
}

void ShowStep1()
{
    var content = CreateStepContent("Step 1 of 3", "Enter your name");
    
    GameDialog.Push(this, content, "NEXT", "CANCEL",
        onOk: () => ShowStep2(),
        onCancel: () => GameDialog.PopAll(this));
}

void ShowStep2()
{
    var content = CreateStepContent("Step 2 of 3", "Choose difficulty");
    
    GameDialog.Push(this, content, "NEXT", "BACK",
        onOk: () => ShowStep3(),
        onCancel: () => GameDialog.Pop(this));
}

void ShowStep3()
{
    var content = CreateStepContent("Step 3 of 3", "Confirm settings");
    
    GameDialog.Push(this, content, "FINISH", "BACK",
        onOk: () => {
            GameDialog.PopAll(this);
            StartGame();
        },
        onCancel: () => GameDialog.Pop(this));
}
```

## Best Practices

1. **Use `Show` for simple dialogs** that don't need stack management
2. **Use `Push` when building complex flows** that users might navigate back through
3. **Always handle both OK and Cancel** actions appropriately
4. **Use `PopAll` for "cancel everything"** scenarios
5. **Set global animations once** at app startup for consistent behavior
6. **Override animation methods** for special dialog types that need unique effects
7. **Check stack count** if you need to know dialog depth for UI decisions

## Notes

- Dialogs automatically remove themselves from the navigation stack when closed
- Empty navigation stacks are automatically cleaned up
- Both `Show` and `Push` add dialogs to the stack - they're functionally equivalent
- `Pop` and `PopAsync` don't return results - they just close dialogs
- Use `ShowAsync` when you need to wait for user input and get a boolean result
