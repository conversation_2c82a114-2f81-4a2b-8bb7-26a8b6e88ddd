using DrawnUi.Draw;
using Microsoft.Maui.Graphics;

namespace BreakoutGame.Game
{
    /// <summary>
    /// A standalone dialog class with blurred background for the Breakout game.
    /// Displays content with optional OK and Cancel buttons.
    /// </summary>
    public class GameDialog : SkiaLayout
    {
        public Action OnOkClicked { get; set; }
        public Action OnCancelClicked { get; set; }

        private SkiaControl _content;
        private string _okText;
        private string _cancelText;

        public GameDialog(SkiaControl content, string ok = null, string cancel = null)
        {
            _content = content;
            _okText = ok ?? "OK";
            _cancelText = cancel;

            SetupDialog();
        }

        private void SetupDialog()
        {
            // Main dialog container
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;
            ZIndex = 200;

            Children = new List<SkiaControl>()
            {
                // Background layer can dim stuff
                new SkiaLayout()
                {
                    HorizontalOptions = LayoutOptions.Fill,
                    VerticalOptions = LayoutOptions.Fill,
                    BackgroundColor = Color.Parse("#66000000"),
                    ZIndex = -1,
                    UseCache = SkiaCacheType.Operations
                },

                // Dialog content container
                new SkiaLayout()
                {
                    Margin = 50,
                    HorizontalOptions = LayoutOptions.Center,
                    VerticalOptions = LayoutOptions.Center,
                    MinimumHeightRequest = 50,
                    //WidthRequest = 330,
                    Children = new List<SkiaControl>()
                    {
                        // Background shape with rounded corners
                        new SkiaShape()
                        {
                            CornerRadius = 14,
                            HorizontalOptions = LayoutOptions.Fill,
                            VerticalOptions = LayoutOptions.Fill,
                            StrokeColor = Colors.White,
                            StrokeWidth = 2,
                            ZIndex = -1,
                            Children =
                            {
                             new SkiaBackdrop()
                                {
                                    Blur = 6,
                                    HorizontalOptions = LayoutOptions.Fill,
                                    VerticalOptions = LayoutOptions.Fill,
                                }
                            }
                        },

                        // Content layout
                        new SkiaLayout()
                        {
                            HorizontalOptions = LayoutOptions.Fill, //todo required for some reason here, check why and fix
                            UseCache = SkiaCacheType.Image,
                            Type = LayoutType.Column,
                            Padding = 24,
                            Spacing = 20,
                            Children = CreateContentChildren()
                        }
                    }
                }
            };
        }

        private List<SkiaControl> CreateContentChildren()
        {
            var children = new List<SkiaControl>();

            // Add the main content
            if (_content != null)
            {
                _content.VerticalOptions = LayoutOptions.Start;
                children.Add(_content);
            }

            // Create buttons layout
            var buttonsLayout = new SkiaLayout()
            {
                Type = LayoutType.Row,
                HorizontalOptions = LayoutOptions.Center,
                Spacing = 16,
                Children =
                {
                    // OK button (always present)
                    new SkiaButton()
                    {
                        Text = _okText,
                        FontSize = 16,
                        FontFamily = "FontGame",
                        TextColor = Colors.White,
                        BackgroundColor = Colors.DarkBlue,
                        //CornerRadius = 8,
                        //Padding = new Thickness(24, 12),
                        WidthRequest = -1,
                        MinimumWidthRequest = 100,
                    }
                    .OnTapped((me) =>
                    {
                        OnOkClicked?.Invoke();
                    })
                }
            };

            // Cancel button (optional)
            if (!string.IsNullOrEmpty(_cancelText))
            {
                var cancelButton = new SkiaButton()
                {
                    Text = _cancelText,
                    FontSize = 16,
                    FontFamily = "FontGame",
                    TextColor = Colors.White,
                    BackgroundColor = Colors.DarkRed,
                    //CornerRadius = 8,
                    //Padding = new Thickness(24, 12),
                    WidthRequest = -1,
                    MinimumWidthRequest = 100,
                }
                .OnTapped((me) =>
                {
                    OnCancelClicked?.Invoke();
                });

                buttonsLayout.Add(cancelButton);
            }

            children.Add(buttonsLayout);

            return children;
        }
    }
}
